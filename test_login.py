import time
from selenium_driverless import webdriver
from selenium_driverless.types.by import By
import asyncio

# Test credentials (use your actual credentials)
email = "<EMAIL>"
password = "sacramento209%%"
element_timeout = 15

async def cloudflare_turnstile_solver(driver):
    """
    Handle Cloudflare Turnstile verification using shadow DOM access.
    The Turnstile widget is embedded in a closed shadow DOM, so we need to use JavaScript to interact with it.
    """
    print("Handling Cloudflare Turnstile verification...")
    
    try:
        # Wait for the Cloudflare Turnstile container to appear
        turnstile_container = await driver.find_element(By.ID, "cf-turnstile", timeout=element_timeout)
        print("Found Cloudflare Turnstile container")
        
        # Check if login button is already enabled (sometimes Turnstile auto-completes)
        login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=5)
        is_disabled = await login_button.get_dom_attribute('disabled')
        
        if is_disabled is None:
            print("Login button is already enabled - Turnst<PERSON> may have auto-completed")
            return True
        
        print("Login button is disabled - waiting for <PERSON><PERSON><PERSON> to load and complete...")
        
        # Wait for Turnstile to fully load (it loads asynchronously)
        await driver.sleep(5)
        
        # Use JavaScript to access the shadow DOM and interact with the Turnstile iframe
        # This script will try to find and click the Turnstile verification element
        turnstile_script = """
        async function handleTurnstile() {
            const turnstileContainer = document.getElementById('cf-turnstile');
            if (!turnstileContainer) {
                return { success: false, message: 'Turnstile container not found' };
            }
            
            // Look for shadow root
            const shadowHost = turnstileContainer.querySelector('div');
            if (!shadowHost || !shadowHost.shadowRoot) {
                return { success: false, message: 'Shadow root not found' };
            }
            
            const shadowRoot = shadowHost.shadowRoot;
            const iframe = shadowRoot.querySelector('iframe');
            
            if (!iframe) {
                return { success: false, message: 'Turnstile iframe not found in shadow DOM' };
            }
            
            // Try to access iframe content (may be blocked by CORS)
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // Look for clickable elements in the iframe
                const clickableSelectors = [
                    'input[type="checkbox"]',
                    'button',
                    '[role="checkbox"]',
                    '[tabindex="0"]',
                    '.cf-turnstile-wrapper',
                    'div[style*="cursor: pointer"]'
                ];
                
                for (const selector of clickableSelectors) {
                    const element = iframeDoc.querySelector(selector);
                    if (element) {
                        element.click();
                        return { success: true, message: `Clicked element with selector: ${selector}` };
                    }
                }
                
                return { success: false, message: 'No clickable elements found in iframe' };
                
            } catch (e) {
                // If we can't access iframe content due to CORS, try clicking the iframe itself
                iframe.click();
                return { success: true, message: 'Clicked iframe directly (CORS blocked content access)' };
            }
        }
        
        return await handleTurnstile();
        """
        
        # Execute the JavaScript to handle Turnstile
        result = await driver.execute_script(turnstile_script)
        
        # Also try direct interaction with the container as fallback
        if not result.get('success', False):
            try:
                await turnstile_container.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0, border=0, scroll_to=False, move_to=False)
                print("Clicked Turnstile container as fallback")
            except:
                pass
        
        print(f"Turnstile interaction result: {result}")
        
        # Wait for verification to complete
        await driver.sleep(8)
        
        # Check if login button is now enabled
        max_attempts = 15
        for attempt in range(max_attempts):
            is_disabled = await login_button.get_dom_attribute('disabled')
            if is_disabled is None:
                print(f"Login button enabled after {attempt + 1} attempts - Turnstile verification successful!")
                return True
            
            await driver.sleep(2)
            print(f"Attempt {attempt + 1}/{max_attempts}: Still waiting for Turnstile completion...")
        
        print("Login button still disabled after all attempts - Turnstile verification may have failed")
        return False
        
    except Exception as e:
        print(f"Error handling Cloudflare Turnstile: {e}")
        return False

async def test_login():
    """Test only the login functionality with Cloudflare Turnstile handling"""
    options = webdriver.ChromeOptions()
    options.binary_location = "C://Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"
    
    async with webdriver.Chrome(options=options) as driver:
        print("Starting login test...")
        
        # Navigate to PirateShip login page
        await driver.get('https://ship.pirateship.com/')
        print("Navigated to PirateShip")
        
        # Enter email
        email_input = await driver.find_element(By.CSS_SELECTOR, "input[name='email']", timeout=element_timeout)
        await email_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await email_input.send_keys(email)
        print("Entered email")
        
        # Enter password
        password_input = await driver.find_element(By.CSS_SELECTOR, "input[name='password']", timeout=element_timeout)
        await password_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await password_input.send_keys(password)
        print("Entered password")
        
        await driver.sleep(2)
        
        # Handle Cloudflare Turnstile verification
        print("Checking for Cloudflare Turnstile verification...")
        cap_res = await cloudflare_turnstile_solver(driver)
        print(f"Cloudflare Turnstile verification result: {cap_res}")
        
        if cap_res == False:
            print("Cloudflare Turnstile verification failed. Exiting...")
            return False
        
        # Click the login button
        login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=element_timeout)
        await login_button.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        print("Clicked login button")
        
        # Wait for login to complete
        await driver.sleep(10)
        
        # Check if login was successful
        current_url = await driver.current_url
        print(f"Current URL after login: {current_url}")
        
        if "ship" in current_url or "dashboard" in current_url:
            print("✅ LOGIN TEST SUCCESSFUL!")
            return True
        else:
            print("❌ LOGIN TEST FAILED - unexpected URL")
            return False

if __name__ == "__main__":
    result = asyncio.run(test_login())
    print(f"\nTest Result: {'PASSED' if result else 'FAILED'}")
