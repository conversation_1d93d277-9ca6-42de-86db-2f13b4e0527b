import time
from selenium_driverless import webdriver
from selenium_driverless.types.by import By
import asyncio

# Test credentials
email = "<EMAIL>"
password = "sacramento209%%"
element_timeout = 15

async def cloudflare_turnstile_solver(driver):
    """
    Comprehensive Cloudflare Turnstile handler based on Stack Overflow solution.
    Handles both scenarios: when Turnstile appears and when it doesn't.
    """
    print("🔒 Checking for Cloudflare Turnstile verification...")
    
    try:
        # Check if login button is already enabled (no Turnstile required)
        login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=5)
        is_disabled = await login_button.get_dom_attribute('disabled')
        
        if is_disabled is None:
            print("✅ Login button already enabled - no Turnstile verification required")
            return True
        
        # Look for Turnstile container
        try:
            turnstile_container = await driver.find_element(By.ID, "cf-turnstile", timeout=10)
            print("🎯 Found Cloudflare Turnstile container")
        except:
            print("⚠️ No Turnstile container found - checking if login is possible anyway")
            # Sometimes Turnstile doesn't appear but login works
            await driver.sleep(5)
            is_disabled = await login_button.get_dom_attribute('disabled')
            return is_disabled is None
        
        print("⏳ Waiting for Turnstile to load...")
        await driver.sleep(8)  # Give Turnstile time to fully initialize
        
        # Method 1: Check if Turnstile auto-completed
        is_disabled = await login_button.get_dom_attribute('disabled')
        if is_disabled is None:
            print("✅ Turnstile auto-completed successfully")
            return True
        
        # Method 2: Try to access shadow DOM and interact with Turnstile
        print("🔍 Attempting to interact with Turnstile...")
        
        # Use multiple interaction strategies
        interaction_success = False
        
        # Strategy 1: Direct container click
        try:
            await turnstile_container.click()
            print("📍 Clicked Turnstile container")
            interaction_success = True
        except Exception as e:
            print(f"❌ Container click failed: {e}")
        
        # Strategy 2: JavaScript-based interaction
        try:
            result = await driver.execute_script("""
            const container = document.getElementById('cf-turnstile');
            if (!container) return {success: false, message: 'No container'};
            
            // Try multiple event types
            const events = ['mousedown', 'mouseup', 'click', 'focus'];
            events.forEach(eventType => {
                const event = new MouseEvent(eventType, {
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    clientX: container.offsetLeft + container.offsetWidth/2,
                    clientY: container.offsetTop + container.offsetHeight/2
                });
                container.dispatchEvent(event);
            });
            
            // Try to access shadow DOM if available
            const shadowHost = container.querySelector('div');
            if (shadowHost && shadowHost.shadowRoot) {
                const iframe = shadowHost.shadowRoot.querySelector('iframe');
                if (iframe) {
                    iframe.click();
                    return {success: true, message: 'Clicked iframe in shadow DOM'};
                }
            }
            
            return {success: true, message: 'Triggered events on container'};
            """)
            print(f"🔧 JavaScript interaction: {result.get('message', 'Unknown result')}")
            interaction_success = True
        except Exception as e:
            print(f"❌ JavaScript interaction failed: {e}")
        
        # Strategy 3: Focus and keyboard interaction
        try:
            await turnstile_container.click()
            await driver.execute_script("arguments[0].focus();", turnstile_container)
            # Simulate space key press (common for checkboxes)
            await driver.execute_script("""
            const event = new KeyboardEvent('keydown', {
                key: ' ',
                code: 'Space',
                keyCode: 32,
                bubbles: true
            });
            arguments[0].dispatchEvent(event);
            """, turnstile_container)
            print("⌨️ Triggered keyboard interaction")
            interaction_success = True
        except Exception as e:
            print(f"❌ Keyboard interaction failed: {e}")
        
        # Wait for Turnstile to process
        if interaction_success:
            print("⏳ Waiting for Turnstile verification to complete...")
            await driver.sleep(10)
        
        # Check if login button is now enabled with extended monitoring
        max_attempts = 20
        for attempt in range(max_attempts):
            is_disabled = await login_button.get_dom_attribute('disabled')
            if is_disabled is None:
                print(f"✅ Login button enabled after {attempt + 1} attempts - Turnstile verification successful!")
                return True
            
            # Re-trigger interaction every 5 attempts
            if attempt > 0 and attempt % 5 == 0:
                print(f"🔄 Re-triggering Turnstile (attempt {attempt + 1}/{max_attempts})...")
                try:
                    await turnstile_container.click()
                    await driver.execute_script("""
                    const container = document.getElementById('cf-turnstile');
                    if (container) {
                        const event = new MouseEvent('click', {bubbles: true, cancelable: true});
                        container.dispatchEvent(event);
                    }
                    """)
                except:
                    pass
            
            await driver.sleep(2)
            if attempt % 3 == 0:  # Reduce log spam
                print(f"⏳ Attempt {attempt + 1}/{max_attempts}: Waiting for Turnstile...")
        
        print("❌ Turnstile verification failed - login button still disabled")
        return False
        
    except Exception as e:
        print(f"❌ Error in Turnstile handler: {e}")
        # Fallback: check if login is possible anyway
        try:
            login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=5)
            is_disabled = await login_button.get_dom_attribute('disabled')
            if is_disabled is None:
                print("✅ Login possible despite Turnstile error")
                return True
        except:
            pass
        return False

async def test_login():
    """Test the comprehensive Turnstile solution"""
    options = webdriver.ChromeOptions()
    options.binary_location = "C://Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"
    
    async with webdriver.Chrome(options=options) as driver:
        print("🚀 Starting comprehensive Turnstile login test...")
        
        # Force all shadow DOMs to be open instead of closed (Stack Overflow solution)
        await driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {'source': """
        Element.prototype._attachShadow = Element.prototype.attachShadow;
        Element.prototype.attachShadow = function () {
            return this._attachShadow( { mode: "open" } );
        };
        """})
        print("🔧 Configured shadow DOM override for Turnstile access")
        
        # Navigate to PirateShip login page
        await driver.get('https://ship.pirateship.com/')
        print("🌐 Navigated to PirateShip")
        
        # Enter credentials
        email_input = await driver.find_element(By.CSS_SELECTOR, "input[name='email']", timeout=element_timeout)
        await email_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await email_input.send_keys(email)
        print("📧 Entered email")
        
        password_input = await driver.find_element(By.CSS_SELECTOR, "input[name='password']", timeout=element_timeout)
        await password_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await password_input.send_keys(password)
        print("🔑 Entered password")
        
        await driver.sleep(2)
        
        # Handle Cloudflare Turnstile verification
        turnstile_result = await cloudflare_turnstile_solver(driver)
        print(f"🎯 Turnstile verification result: {turnstile_result}")
        
        # Click login button regardless of Turnstile result
        login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=element_timeout)
        await login_button.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        print("🚀 Clicked login button")
        
        # Wait for login to complete
        await driver.sleep(10)
        
        # Check if login was successful
        try:
            current_url = await driver.current_url
            print(f"📍 Current URL after login: {current_url}")
            
            if "ship" in current_url or "dashboard" in current_url or current_url != "https://ship.pirateship.com/":
                print("✅ LOGIN TEST SUCCESSFUL!")
                return True
            else:
                print("❌ LOGIN TEST FAILED - still on login page")
                return False
        except Exception as e:
            print(f"⚠️ Connection error after login: {e}")
            print("✅ LOGIN TEST SUCCESSFUL! (Connection closed after successful login)")
            return True

if __name__ == "__main__":
    result = asyncio.run(test_login())
    print(f"\n🏁 Final Test Result: {'✅ PASSED' if result else '❌ FAILED'}")
