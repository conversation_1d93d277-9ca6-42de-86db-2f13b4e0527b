import time
from selenium_driverless import webdriver
from selenium_driverless.types.by import By
import asyncio

# Test credentials
email = "<EMAIL>"
password = "sacramento209%%"
element_timeout = 15

async def cloudflare_turnstile_solver(driver):
    """
    Improved Cloudflare Turnstile handler that waits for natural completion.
    Avoids clicking on informational elements that open "Learn more" pages.
    """
    print("🔒 Checking for Cloudflare Turnstile verification...")

    try:
        # Check if login button is already enabled (no Turnstile required)
        login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=5)
        is_disabled = await login_button.get_dom_attribute('disabled')

        if is_disabled is None:
            print("✅ Login button already enabled - no Turnstile verification required")
            return True

        # Look for Turnstile container
        try:
            turnstile_container = await driver.find_element(By.ID, "cf-turnstile", timeout=10)
            print("🎯 Found Cloudflare Turnstile container")
        except:
            print("⚠️ No Turnstile container found - checking if login is possible anyway")
            await driver.sleep(5)
            is_disabled = await login_button.get_dom_attribute('disabled')
            return is_disabled is None

        print("⏳ Waiting for Turnstile to fully load and initialize...")

        # Wait longer for Turnstile to load and potentially auto-complete
        max_wait_attempts = 30  # 60 seconds total
        for attempt in range(max_wait_attempts):
            await driver.sleep(2)

            # Check if login button became enabled (Turnstile completed)
            is_disabled = await login_button.get_dom_attribute('disabled')
            if is_disabled is None:
                print(f"✅ Turnstile completed automatically after {(attempt + 1) * 2} seconds!")
                return True

            # Every 10 seconds, check if Turnstile iframe is loaded and ready
            if attempt % 5 == 0:
                try:
                    # Check if Turnstile iframe is present and loaded
                    iframe_check = await driver.execute_script("""
                    const container = document.getElementById('cf-turnstile');
                    if (!container) return {loaded: false, message: 'No container'};

                    const shadowHost = container.querySelector('div');
                    if (shadowHost && shadowHost.shadowRoot) {
                        const iframe = shadowHost.shadowRoot.querySelector('iframe');
                        if (iframe && iframe.src) {
                            return {
                                loaded: true,
                                message: 'Iframe loaded',
                                src: iframe.src,
                                width: iframe.offsetWidth,
                                height: iframe.offsetHeight
                            };
                        }
                    }
                    return {loaded: false, message: 'Iframe not ready'};
                    """)

                    if iframe_check.get('loaded'):
                        print(f"📱 Turnstile iframe loaded: {iframe_check.get('message')}")

                        # Only try gentle interaction if iframe is properly loaded
                        if attempt > 10:  # Only after waiting at least 20 seconds
                            print("🤏 Attempting gentle interaction with loaded Turnstile...")
                            try:
                                # Very gentle click on the center of the container
                                await driver.execute_script("""
                                const container = document.getElementById('cf-turnstile');
                                if (container) {
                                    const rect = container.getBoundingClientRect();
                                    const centerX = rect.left + rect.width / 2;
                                    const centerY = rect.top + rect.height / 2;

                                    const event = new MouseEvent('click', {
                                        view: window,
                                        bubbles: true,
                                        cancelable: true,
                                        clientX: centerX,
                                        clientY: centerY
                                    });
                                    container.dispatchEvent(event);
                                }
                                """)
                                print("🎯 Gentle click attempted")
                            except Exception as gentle_error:
                                print(f"⚠️ Gentle interaction failed: {gentle_error}")
                    else:
                        print(f"⏳ Waiting for Turnstile to load... ({iframe_check.get('message')})")

                except Exception as check_error:
                    print(f"⚠️ Error checking Turnstile status: {check_error}")

            # Progress indicator
            if attempt % 10 == 0 and attempt > 0:
                print(f"⏳ Still waiting for Turnstile... ({attempt * 2}s elapsed)")

        print("❌ Turnstile did not complete within timeout period")
        return False

    except Exception as e:
        print(f"❌ Error in Turnstile handler: {e}")
        # Fallback: check if login is possible anyway
        try:
            login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=5)
            is_disabled = await login_button.get_dom_attribute('disabled')
            if is_disabled is None:
                print("✅ Login possible despite Turnstile error")
                return True
        except:
            pass
        return False

async def test_login():
    """Test the comprehensive Turnstile solution"""
    options = webdriver.ChromeOptions()
    options.binary_location = "C://Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"
    
    async with webdriver.Chrome(options=options) as driver:
        print("🚀 Starting comprehensive Turnstile login test...")
        
        # Force all shadow DOMs to be open instead of closed (Stack Overflow solution)
        await driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {'source': """
        Element.prototype._attachShadow = Element.prototype.attachShadow;
        Element.prototype.attachShadow = function () {
            return this._attachShadow( { mode: "open" } );
        };
        """})
        print("🔧 Configured shadow DOM override for Turnstile access")
        
        # Navigate to PirateShip login page
        await driver.get('https://ship.pirateship.com/')
        print("🌐 Navigated to PirateShip")
        
        # Enter credentials
        email_input = await driver.find_element(By.CSS_SELECTOR, "input[name='email']", timeout=element_timeout)
        await email_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await email_input.send_keys(email)
        print("📧 Entered email")
        
        password_input = await driver.find_element(By.CSS_SELECTOR, "input[name='password']", timeout=element_timeout)
        await password_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await password_input.send_keys(password)
        print("🔑 Entered password")
        
        await driver.sleep(2)
        
        # Handle Cloudflare Turnstile verification
        turnstile_result = await cloudflare_turnstile_solver(driver)
        print(f"🎯 Turnstile verification result: {turnstile_result}")
        
        # Click login button regardless of Turnstile result
        login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=element_timeout)
        await login_button.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        print("🚀 Clicked login button")
        
        # Wait for login to complete
        await driver.sleep(10)

        # Check for new tabs (indicates we clicked a "Learn more" link)
        try:
            targets = await driver.targets
            if len(targets) > 1:
                print("⚠️ WARNING: Multiple tabs detected - may have clicked 'Learn more' link")
                for target_id, target_info in targets.items():
                    try:
                        url = await target_info.Target.current_url
                        print(f"   Tab: {url}")
                    except:
                        pass
        except Exception as e:
            print(f"⚠️ Could not check for multiple tabs: {e}")

        # Check if login was successful by looking for specific elements
        try:
            current_url = await driver.current_url
            print(f"📍 Current URL after login: {current_url}")

            # More accurate success detection
            if current_url == "https://ship.pirateship.com/":
                # Still on login page - check if there are any error messages
                try:
                    # Look for login form still being present
                    login_form = await driver.find_element(By.CSS_SELECTOR, "input[name='email']", timeout=3)
                    print("❌ LOGIN TEST FAILED - still on login page (login form still present)")
                    return False
                except:
                    # Login form not found, might have succeeded
                    print("🤔 Login form not found - checking for dashboard elements...")

            # Check for successful login indicators
            try:
                # Look for elements that appear after successful login
                dashboard_elements = [
                    "action-btn-label",  # Ship button
                    "shipment-copypaste",  # Address input
                    ".nav-link",  # Navigation links
                ]

                for selector in dashboard_elements:
                    try:
                        element = await driver.find_element(By.CSS_SELECTOR, selector, timeout=3)
                        print(f"✅ Found dashboard element: {selector}")
                        print("✅ LOGIN TEST SUCCESSFUL!")
                        return True
                    except:
                        continue

                print("❌ LOGIN TEST FAILED - no dashboard elements found")
                return False

            except Exception as check_error:
                print(f"⚠️ Error checking for dashboard elements: {check_error}")
                return False

        except Exception as e:
            print(f"⚠️ Connection error after login: {e}")
            # Connection errors might indicate successful login (page redirect)
            return False

if __name__ == "__main__":
    result = asyncio.run(test_login())
    print(f"\n🏁 Final Test Result: {'✅ PASSED' if result else '❌ FAILED'}")
