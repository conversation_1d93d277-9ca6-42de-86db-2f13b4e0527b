import random
import time
from bs4 import BeautifulSoup
from selenium_driverless import webdriver
from selenium_driverless.types.by import By
import asyncio
import requests
import os
import whisper
import uuid
from pydub import AudioSegment
import speech_recognition as sr

model = whisper.load_model("base")
element_timeout = 10

async def transcribe(url):
    with open('.temp', 'wb') as f:
        f.write(requests.get(url).content)
        
    result = model.transcribe('.temp')
    print("AI: ", result["text"].strip())
    return result["text"].strip()


async def main():
    options = webdriver.ChromeOptions()
    proxy = "*************:8080"

    options.binary_location = "C://Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"

    async with webdriver.Chrome(options=options) as driver:
        
        await driver.get("https://www.google.com/recaptcha/api2/demo")
        print("Wait 1") 
                    
        recaptcha_iframe = await driver.find_element(By.XPATH, '//iframe[@title="reCAPTCHA"]', timeout=element_timeout)
        await driver.switch_to.frame(recaptcha_iframe)
        capcha_button = await driver.find_element(By.ID, "recaptcha-anchor-label", timeout=element_timeout)
        await capcha_button.click()
        
        print("Wait 2") 
        await driver.sleep(3)

        second_url = "https://www.google.com/recaptcha/api2/demo"
        targets = await driver.targets
        temp_target = None
        for target_id, target_info in targets.items():
            url = await target_info.Target.current_url
            if second_url in url:
                title = target_info.title
                tar = await target_info.Target
                iframes = await tar.find_elements(By.TAG_NAME, "iframe")
                for index, iframe in enumerate(iframes):
                        if iframe != None:
                            src = await iframe.get_dom_attribute('src')
                            if src != None and "https://www.google.com/recaptcha/api2/bframe?hl=en&v=":
                                print(f"Iframe {index + 1}:")
                                print(f"  src: {src}")
                                print(f"  title: {await iframe.get_dom_attribute('title')}")
                                print(f"  name: {await iframe.get_dom_attribute('name')}")
                                print(f"  id: {await iframe.get_dom_attribute('id')}")
                                temp_target = iframe
                                break
                            
        print("Found Captcha IFrame")
        
        second_recaptcha_iframe = await temp_target.find_element(By.XPATH, "//iframe[contains(@title, 'recaptcha challenge expires in two minutes')]", timeout=5)  
        print("Wait 3") 

        temp_fram = await second_recaptcha_iframe.content_document
        
        audio_button = await temp_fram.find_element(By.ID, 'recaptcha-audio-button', timeout=element_timeout) #"Get an audio challenge"
        await audio_button.click()

        print("Wait 4") 
        await driver.sleep(3) #Waiting for //div[normalize-space()="Multiple correct solutions required - please solve more."]'
                    
        try:
            download_link = await temp_fram.find_element(By.CLASS_NAME, 'rc-audiochallenge-tdownload-link', timeout=element_timeout)
            #print(download_link)

        except:
            frame_check = await temp_fram.find_element(By.CLASS_NAME, 'rc-doscaptcha-body-text', timeout=element_timeout)
            print(await frame_check.text)
            #raise Exception
            print('Google has detected automated queries. Try again later.')
            await driver.sleep(2)
 
        link = await download_link.get_attribute('href')
        print("wait 5", link)
        text = await transcribe(link)
        
        solve_audio = await temp_fram.find_element(By.ID, "audio-response", timeout=element_timeout)
        await solve_audio.send_keys(text)
        
        verify_button = await temp_fram.find_element(By.ID, "recaptcha-verify-button", timeout=element_timeout)
        await verify_button.click()
        
        print("Wait done fuck capcha") 
        await driver.sleep(30)
        await driver.quit()

asyncio.run(main())

